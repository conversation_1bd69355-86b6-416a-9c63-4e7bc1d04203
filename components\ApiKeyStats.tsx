import React, { useState, useEffect } from 'react';
import { apiKeyManager } from '../services/apiKeyManager';

interface ApiKeyStatsProps {
    isVisible: boolean;
    onClose: () => void;
}

const ApiKeyStats: React.FC<ApiKeyStatsProps> = ({ isVisible, onClose }) => {
    const [stats, setStats] = useState<Record<string, any>>({});
    const [refreshKey, setRefreshKey] = useState(0);

    useEffect(() => {
        if (isVisible) {
            const loadStats = () => {
                const currentStats = apiKeyManager.getStats();
                setStats(currentStats);
            };

            loadStats();
            const interval = setInterval(loadStats, 2000); // Actualiser toutes les 2 secondes

            return () => clearInterval(interval);
        }
    }, [isVisible, refreshKey]);

    const handleResetBlacklists = () => {
        apiKeyManager.resetBlacklists();
        setRefreshKey(prev => prev + 1);
    };

    if (!isVisible) return null;

    const totalKeys = Object.keys(stats).length;
    const activeKeys = Object.values(stats).filter((stat: any) => !stat.isBlacklisted).length;
    const blacklistedKeys = totalKeys - activeKeys;

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 max-w-4xl max-h-[80vh] overflow-y-auto">
                <div className="flex justify-between items-center mb-4">
                    <h2 className="text-xl font-bold">📊 Statistiques des Clés API</h2>
                    <button
                        onClick={onClose}
                        className="text-gray-500 hover:text-gray-700 text-xl"
                    >
                        ✕
                    </button>
                </div>

                {/* Résumé global */}
                <div className="grid grid-cols-3 gap-4 mb-6">
                    <div className="bg-blue-100 p-4 rounded-lg text-center">
                        <div className="text-2xl font-bold text-blue-600">{totalKeys}</div>
                        <div className="text-sm text-blue-800">Clés Totales</div>
                    </div>
                    <div className="bg-green-100 p-4 rounded-lg text-center">
                        <div className="text-2xl font-bold text-green-600">{activeKeys}</div>
                        <div className="text-sm text-green-800">Clés Actives</div>
                    </div>
                    <div className="bg-red-100 p-4 rounded-lg text-center">
                        <div className="text-2xl font-bold text-red-600">{blacklistedKeys}</div>
                        <div className="text-sm text-red-800">Clés Bloquées</div>
                    </div>
                </div>

                {/* Actions */}
                <div className="mb-4">
                    <button
                        onClick={handleResetBlacklists}
                        className="bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded-lg mr-2"
                    >
                        🔄 Réinitialiser les Blocages
                    </button>
                    <button
                        onClick={() => setRefreshKey(prev => prev + 1)}
                        className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg"
                    >
                        🔄 Actualiser
                    </button>
                </div>

                {/* Détails par clé */}
                <div className="space-y-3">
                    {Object.entries(stats).map(([keyName, stat]: [string, any]) => {
                        const successRate = stat.totalRequests > 0 
                            ? ((stat.successfulRequests / stat.totalRequests) * 100).toFixed(1)
                            : '0.0';

                        const isBlacklisted = stat.isBlacklisted;
                        const timeUntilUnblock = stat.blacklistUntil 
                            ? Math.max(0, Math.ceil((stat.blacklistUntil - Date.now()) / 1000))
                            : 0;

                        return (
                            <div
                                key={keyName}
                                className={`p-4 rounded-lg border ${
                                    isBlacklisted 
                                        ? 'bg-red-50 border-red-200' 
                                        : 'bg-green-50 border-green-200'
                                }`}
                            >
                                <div className="flex justify-between items-start">
                                    <div className="flex-1">
                                        <h3 className="font-semibold text-lg">
                                            {isBlacklisted ? '🚫' : '✅'} {keyName}
                                        </h3>
                                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-2 text-sm">
                                            <div>
                                                <span className="font-medium">Requêtes:</span>
                                                <div>{stat.totalRequests}</div>
                                            </div>
                                            <div>
                                                <span className="font-medium">Succès:</span>
                                                <div className="text-green-600">{stat.successfulRequests}</div>
                                            </div>
                                            <div>
                                                <span className="font-medium">Échecs:</span>
                                                <div className="text-red-600">{stat.failedRequests}</div>
                                            </div>
                                            <div>
                                                <span className="font-medium">Taux de succès:</span>
                                                <div className={`font-bold ${
                                                    parseFloat(successRate) > 80 ? 'text-green-600' :
                                                    parseFloat(successRate) > 50 ? 'text-yellow-600' : 'text-red-600'
                                                }`}>
                                                    {successRate}%
                                                </div>
                                            </div>
                                        </div>

                                        {/* Erreurs par type */}
                                        {Object.keys(stat.errorTypes).length > 0 && (
                                            <div className="mt-2">
                                                <span className="font-medium text-sm">Erreurs:</span>
                                                <div className="flex flex-wrap gap-2 mt-1">
                                                    {Object.entries(stat.errorTypes).map(([errorCode, count]: [string, any]) => (
                                                        <span
                                                            key={errorCode}
                                                            className="bg-red-100 text-red-800 px-2 py-1 rounded text-xs"
                                                        >
                                                            {errorCode}: {count}
                                                        </span>
                                                    ))}
                                                </div>
                                            </div>
                                        )}

                                        {/* Statut de blocage */}
                                        {isBlacklisted && (
                                            <div className="mt-2">
                                                <span className="bg-red-100 text-red-800 px-2 py-1 rounded text-sm">
                                                    {timeUntilUnblock > 0 
                                                        ? `Bloquée pour ${timeUntilUnblock}s`
                                                        : 'Bloquée (échecs consécutifs)'
                                                    }
                                                </span>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </div>
                        );
                    })}
                </div>

                {Object.keys(stats).length === 0 && (
                    <div className="text-center text-gray-500 py-8">
                        Aucune statistique disponible. Effectuez quelques requêtes pour voir les données.
                    </div>
                )}
            </div>
        </div>
    );
};

export default ApiKeyStats;
